"""
提供基于提示词的模型问答
模型+MCP的简单Agent文档
多agent组成的team问答
"""

import asyncio
import json
import os
from pathlib import Path
from typing import Any, Dict, Optional, Union

from autogen_agentchat.teams import RoundRobinGroupChat
from sqlmodel import Session, create_engine, select
from db import Team
from autogen_agentchat.messages import TextMessage
from io import BytesIO

import requests
from autogen_agentchat.messages import MultiModalMessage
from autogen_core import Image as AGImage
from PIL import Image
from autogen_core.models import UserMessage
from autogen_ext.models.ollama import OllamaChatCompletionClient
from autogen_ext.models.openai import OpenAIChatCompletionClient
from autogen_ext.tools.mcp import StreamableHttpServerParams,McpWorkbench
from autogen_agentchat.agents import AssistantAgent
import contextlib
import jwt

JWT_SECRET = "xingchen-chatbot-secret-key-2025"
IMAGE_PROXY_URL = "http://127.0.0.1:4000/files/"
def load_team_from_id(team_id: int) -> Optional[Dict[str, Any]]:
    """Load team configuration from database by team ID

    Args:
        team_id: The ID of the team to load from the database

    Returns:
        The team component configuration as a dictionary, or None if not found

    Raises:
        Exception: If database connection or query fails
    """
    # Construct database path: %Home%\.autogenstudio\autogen04203.db
    home_dir = Path.home()
    db_path = home_dir / ".autogenstudio" / "autogen04203.db"

    # Create database URI
    db_uri = f"sqlite:///{db_path}"

    # Create database engine with SQLite-specific settings
    engine = create_engine(db_uri, connect_args={"check_same_thread": False})

    try:
        with Session(engine) as session:
            # Query team by ID
            statement = select(Team).where(Team.id == team_id)
            team = session.exec(statement).first()

            if team is None:
                return None

            # Return the component field (JSON data)
            return team.component

    except Exception as e:
        raise Exception(f"Failed to load team from database: {str(e)}")
    finally:
        # Clean up engine
        engine.dispose()


async def load_team_from_file(path: Union[str, Path]) -> Dict[str, Any]:
    """Load team configuration from JSON file"""
    path = Path(path)
    if not path.exists():
        raise FileNotFoundError(f"Team config file not found: {path}")
    
    with open(path, 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    return config


def create_team_from_config(config: Dict[str, Any]):
    """Create team instance from configuration dictionary"""
    try:
        # Use AutoGen's component loading system
        team = RoundRobinGroupChat.load_component(config)
        return team
    except Exception as e:
        print(f"Failed to create team: {e}")
        raise


async def load_and_run_team(
    team_config_path: str, 
    task: str,
    api_key: Optional[str] = None,
    openai_base_url: Optional[str] = None
) -> Any:
    """Complete workflow: load team config and run task"""
    
    # Set environment variables if provided
    if api_key:
        os.environ["OPENAI_API_KEY"] = api_key
    if openai_base_url:
        os.environ["OPENAI_BASE_URL"] = openai_base_url
    
    # Load team configuration
    config = await load_team_from_file(team_config_path)
    print(f"Loaded team config from {team_config_path}")
    
    # Create team instance
    team = create_team_from_config(config)
    print(f"Created team instance: {type(team).__name__}")
    
    # Run the task
    print(f"Running task: {task}")
    result = await team.run(task=task)
    
    return result

async def ollama_model_test():
    # Assuming your Ollama server is running locally on port 11434.
    model_client = OllamaChatCompletionClient(model="qwen3:latest")
    # response = await model_client.create([UserMessage(content="What is the capital of France?", source="user")])
    # print(response)
    mcp_server1 = StreamableHttpServerParams(
        url="http://localhost:11435/mcp",
        headers={"Authorization": "Bearer your-api-key", "Content-Type": "application/json"},
        timeout=30,
        sse_read_timeout=60 * 5,
        terminate_on_close=True,
    )
    mcp_server2 = StreamableHttpServerParams(
        url="http://localhost:1111/mcp",
        headers={"Authorization": "Bearer your-api-key", "Content-Type": "application/json"},
        timeout=30,
        sse_read_timeout=60 * 5,
        terminate_on_close=True,
    )
    mcp_server_list = [mcp_server1,mcp_server2]
    # Create an MCP workbench which provides a session to the mcp server.
    async with contextlib.AsyncExitStack() as stack:
        workbenches = []
        for mcp_server in mcp_server_list:
            workbench = await stack.enter_async_context(McpWorkbench(mcp_server))
            workbenches.append(workbench)
        # Create an agent that can use the fetch tool.
        mcp_agent = AssistantAgent(
            name="mcp_agent", model_client=model_client, workbench=workbenches, reflect_on_tool_use=True
        )

        # Let the agent fetch the content of a URL and summarize it.
        result = await mcp_agent.run(task="calculate 11937*33887")
        assert isinstance(result.messages[-1], TextMessage)
        print(result.messages[-1].content)

        # Close the connection to the model client.
        await model_client.close()
async def chat_backend(messages,model_name=None,model_type=None,api_key=None,openai_base_url=None,stream=True,MCP_url_list=[],team_id=None):
    ag_messages = []
    for i in messages:
        if i["type"]=="text":
            ag_messages.append(TextMessage(content=i["content"], source=i["role"]))
        elif i["type"]=="image":
            img_url = IMAGE_PROXY_URL+i["content"]
            print(img_url)
            pil_image = Image.open(BytesIO(requests.get(img_url).content))
            img = AGImage(pil_image)
            ag_messages.append(MultiModalMessage(content=[f"img_url:{img_url}",img], source=i["role"]))

    if team_id:
        os.environ["OPENAI_API_KEY"] = api_key
        os.environ["OPENAI_BASE_URL"] = openai_base_url
        config = load_team_from_id(team_id)
        team = create_team_from_config(config)

        if stream:
            # Return streaming response for team
            message_count = 0
            async for message in team.run_stream(task=ag_messages):
                message_count += 1
                # Skip the original messages
                if message_count > len(ag_messages) and message.source not in ["User", "System"]:
                    # Use dump() method for proper serialization
                    if hasattr(message, 'dump'):
                        yield message.dump()
                    else:
                        yield dict(message)
        else:
            # Return non-streaming response for team
            result = await team.run(task=ag_messages)
            return_result = []
            for i in result.messages[len(ag_messages):]:
                # Use dump() method for proper serialization
                if hasattr(i, 'dump'):
                    return_result.append(i.dump())
                else:
                    return_result.append(dict(i))
            print(return_result)
            yield return_result

    elif model_type=="ollama":
        model_client = OllamaChatCompletionClient(model=model_name)
        # Create MCP workbenches if MCP URLs are provided
        workbenches = []
        if MCP_url_list:
            async with contextlib.AsyncExitStack() as stack:
                for mcp_url in MCP_url_list:
                    mcp_server = StreamableHttpServerParams(
                        url=mcp_url,
                        headers={"Authorization": "Bearer your-api-key", "Content-Type": "application/json"},
                        timeout=30,
                        sse_read_timeout=60 * 5,
                        terminate_on_close=True,
                    )
                    workbench = await stack.enter_async_context(McpWorkbench(mcp_server))
                    workbenches.append(workbench)

                # Create agent with MCP workbenches
                agent = AssistantAgent(
                    name="ollama_agent",
                    model_client=model_client,
                    workbench=workbenches,
                    reflect_on_tool_use=True
                )

                if stream:
                    # Return streaming response
                    message_count = 0
                    async for message in agent.run_stream(task=ag_messages):
                        message_count += 1
                        # Skip the original messages
                        if message_count > len(ag_messages):
                            yield dict(message)
                else:
                    # Return non-streaming response
                    result = await agent.run(task=ag_messages)
                    return_result = []
                    for i in result.messages[len(ag_messages):]:
                        return_result.append(dict(i))
                    yield return_result
                await model_client.close()
        else:
            # Create simple agent without MCP
            agent = AssistantAgent(
                name="ollama_agent",
                model_client=model_client
            )

            if stream:
                # Return streaming response
                message_count = 0
                async for message in agent.run_stream(task=ag_messages):
                    message_count += 1
                    # Skip the original messages
                    if message_count > len(ag_messages):
                        yield dict(message)
            else:
                # Return non-streaming response
                result = await agent.run(task=ag_messages)
                return_result = []
                for i in result.messages[len(ag_messages):]:
                    return_result.append(dict(i))
                yield return_result
            await model_client.close()

            await model_client.close()

    elif model_type=="openai":
        os.environ["OPENAI_API_KEY"] = api_key
        os.environ["OPENAI_BASE_URL"] = openai_base_url

        model_client = OpenAIChatCompletionClient(model=model_name)

        # Create MCP workbenches if MCP URLs are provided
        workbenches = []
        if MCP_url_list:
            async with contextlib.AsyncExitStack() as stack:
                for mcp_url in MCP_url_list:
                    mcp_server = StreamableHttpServerParams(
                        url=mcp_url,
                        headers={"Authorization": "Bearer your-api-key", "Content-Type": "application/json"},
                        timeout=30,
                        sse_read_timeout=60 * 5,
                        terminate_on_close=True,
                    )
                    workbench = await stack.enter_async_context(McpWorkbench(mcp_server))
                    workbenches.append(workbench)

                # Create agent with MCP workbenches
                agent = AssistantAgent(
                    name="openai_agent",
                    model_client=model_client,
                    workbench=workbenches,
                    reflect_on_tool_use=True
                )

                if stream:
                    # Return streaming response
                    message_count = 0
                    async for message in agent.run_stream(task=ag_messages):
                        message_count += 1
                        # Skip the original messages
                        if message_count > len(ag_messages):
                            yield dict(message)
                else:
                    # Return non-streaming response
                    result = await agent.run(task=ag_messages)
                    return_result = []
                    for i in result.messages[len(ag_messages):]:
                        return_result.append(dict(i))
                    yield return_result
                await model_client.close()

                await model_client.close()
        else:
            # Create simple agent without MCP
            agent = AssistantAgent(
                name="openai_agent",
                model_client=model_client
            )

            if stream:
                # Return streaming response
                message_count = 0
                async for message in agent.run_stream(task=ag_messages):
                    message_count += 1
                    # Skip the original messages
                    if message_count > len(ag_messages):
                        yield dict(message)
            else:
                # Return non-streaming response
                result = await agent.run(task=ag_messages)
                return_result = []
                for i in result.messages[len(ag_messages):]:
                    return_result.append(dict(i))
                yield return_result
            await model_client.close()
async def test(stream=False):
    """Main execution function"""
    # Set API credentials
    os.environ["OPENAI_API_KEY"] = "sk-aOnHdMDqN5SXgxwAC50298EbCcA54345B906AfC7F5D7B384"
    os.environ["OPENAI_BASE_URL"] = "https://gnomic.nengyongai.cn/v1"
    
    try:
        # Load team configuration
        #config = await load_team_from_file("test_team.json")
        config = load_team_from_id(6)
        # Create team instance
        team = create_team_from_config(config)
        if not stream:
            # Run task
            result = await team.run(task="1+1 = ?",)
            print("Task completed successfully!")
            print("Result:", result)
        else:
            text_message0 = TextMessage(content="You are a web tool", source="System")
            text_message1 = TextMessage(content="Search Most import ten things at today", source="User")
            # pil_image = Image.open(BytesIO(requests.get("https://picsum.photos/300/200").content))
            # img = AGImage(pil_image)
            # multi_modal_message = MultiModalMessage(content=["Can you describe the content of this image?", img], source="User")

            stream = team.run_stream(task=[text_message0,text_message1])
            async for message in stream:
                if message.source not  in ["User","System"]:
                    print(message.source,":",message.content)
    except FileNotFoundError:
        print("Error: test_team.json not found. Please ensure the team config file exists.")
    except Exception as e:
        print(f"Error: {e}")


if __name__ == "__main__":
    asyncio.run(ollama_model_test())
