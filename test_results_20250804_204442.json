{"TestSuite": "AutoGen Studio Chat API Tests", "ExecutionTime": "2025-08-04 20:45:09", "Summary": {"SuccessRate": "80%", "TotalTests": 15, "PassedTests": 12, "FailedTests": 3}, "Configuration": {"BaseURL": "http://localhost:1113", "OpenAIBaseURL": "https://gnomic.nengyongai.cn/v1", "APIKey": "sk-aOnHdMDqN5SXgxwAC50298EbCcA54345B906AfC7F5D7B384"}, "TestResults": [{"TestDescription": "获取API基本信息", "Timestamp": "2025-08-04 20:44:44", "Request": {"Endpoint": "/", "Method": "GET", "Body": null}, "Response": {"Content": "{\"message\":\"AutoGen Studio Chat API\",\"version\":\"1.0.0\",\"endpoints\":{\"chat\":\"/chat\",\"chat_stream\":\"/chat/stream\",\"health\":\"/health\"}}", "ErrorMessage": "", "StatusCode": 200, "Success": true}, "TestName": "Test1_RootEndpoint"}, {"TestDescription": "健康检查", "Timestamp": "2025-08-04 20:44:44", "Request": {"Endpoint": "/health", "Method": "GET", "Body": null}, "Response": {"Content": "{\"status\":\"healthy\",\"service\":\"autogen-studio-chat-api\"}", "ErrorMessage": "", "StatusCode": 200, "Success": true}, "TestName": "Test2_HealthCheck"}, {"TestDescription": "非流式聊天 - 基本对话", "Timestamp": "2025-08-04 20:44:47", "Request": {"Endpoint": "/chat", "Method": "POST", "Body": {"messages": [{"content": "你好，请介绍一下你自己", "type": "text", "role": "user"}], "model_type": "openai", "openai_base_url": "https://gnomic.nengyongai.cn/v1", "stream": false, "api_key": "sk-aOnHdMDqN5SXgxwAC50298EbCcA54345B906AfC7F5D7B384", "model_name": "gpt-3.5-turbo"}}, "Response": {"Content": "{\"type\":\"team_result\",\"content\":\"[\\n  {\\n    \\\"id\\\": \\\"5db36109-66ae-4e47-a880-241e271c601d\\\",\\n    \\\"source\\\": \\\"openai_agent\\\",\\n    \\\"models_usage\\\": \\\"RequestUsage(prompt_tokens=48, completion_tokens=52)\\\",\\n    \\\"metadata\\\": {},\\n    \\\"created_at\\\": \\\"2025-08-04 12:44:46.995288+00:00\\\",\\n    \\\"content\\\": \\\"你好！我是一个AI助手，可以帮助您解决问题，回答疑问，提供信息和建议。有什么我可以帮助您的吗？\\\",\\n    \\\"type\\\": \\\"TextMessage\\\"\\n  }\\n]\",\"source\":\"team\"}", "ErrorMessage": "", "StatusCode": 200, "Success": true}, "TestName": "Test3_NonStreamChat_Basic"}, {"TestDescription": "非流式聊天 - 机器学习问题", "Timestamp": "2025-08-04 20:44:50", "Request": {"Endpoint": "/chat", "Method": "POST", "Body": {"messages": [{"content": "解释一下机器学习的基本概念", "type": "text", "role": "user"}], "model_type": "openai", "openai_base_url": "https://gnomic.nengyongai.cn/v1", "stream": false, "api_key": "sk-aOnHdMDqN5SXgxwAC50298EbCcA54345B906AfC7F5D7B384", "model_name": "gpt-3.5-turbo"}}, "Response": {"Content": "{\"type\":\"team_result\",\"content\":\"[\\n  {\\n    \\\"id\\\": \\\"d6a73572-5ee3-4402-ad1d-c71fa5ab06e2\\\",\\n    \\\"source\\\": \\\"openai_agent\\\",\\n    \\\"models_usage\\\": \\\"RequestUsage(prompt_tokens=52, completion_tokens=211)\\\",\\n    \\\"metadata\\\": {},\\n    \\\"created_at\\\": \\\"2025-08-04 12:44:50.390059+00:00\\\",\\n    \\\"content\\\": \\\"机器学习是一种人工智能的应用，通过让计算机学习数据模式和规律来实现特定任务而无需明确编程。其基本概念包括监督学习、无监督学习和强化学习。在监督学习中，算法从带有标签的数据中学习规律，预测未知数据的标签；无监督学习是指从非标签数据中学习模式和结构；强化学习则通过尝试最大化奖励来学习如何做出决策。这些是机器学习的基本概念之一，帮助计算机从数据中学习并改进表现。\\\\n\\\\nTerminate.\\\",\\n    \\\"type\\\": \\\"TextMessage\\\"\\n  }\\n]\",\"source\":\"team\"}", "ErrorMessage": "", "StatusCode": 200, "Success": true}, "TestName": "Test4_NonStreamChat_ML"}, {"TestDescription": "非流式聊天 - 带API密钥", "Timestamp": "2025-08-04 20:44:52", "Request": {"Endpoint": "/chat", "Method": "POST", "Body": {"messages": [{"content": "写一个Python函数来计算斐波那契数列", "type": "text", "role": "user"}], "model_type": "openai", "openai_base_url": "https://gnomic.nengyongai.cn/v1", "stream": false, "api_key": "sk-aOnHdMDqN5SXgxwAC50298EbCcA54345B906AfC7F5D7B384", "model_name": "gpt-3.5-turbo"}}, "Response": {"Content": "{\"type\":\"team_result\",\"content\":\"[\\n  {\\n    \\\"id\\\": \\\"15950a94-0ebf-4c9c-a06e-5889f2a6b95f\\\",\\n    \\\"source\\\": \\\"openai_agent\\\",\\n    \\\"models_usage\\\": \\\"RequestUsage(prompt_tokens=53, completion_tokens=127)\\\",\\n    \\\"metadata\\\": {},\\n    \\\"created_at\\\": \\\"2025-08-04 12:44:52.774001+00:00\\\",\\n    \\\"content\\\": \\\"以下是一个计算斐波那契数列的Python函数：\\\\n\\\\n```python\\\\ndef fibonacci(n):\\\\n    a, b = 0, 1\\\\n    result = []\\\\n    for _ in range(n):\\\\n        result.append(a)\\\\n        a, b = b, a + b\\\\n    return result\\\\n\\\\nprint(fibonacci(10))\\\\n```\\\\n\\\\n这个函数将生成前n个斐波那契数列并返回结果。您可以调用该函数来获取所需长度的斐波那契数列序列。\\\\n\\\\nTERMINATE\\\",\\n    \\\"type\\\": \\\"TextMessage\\\"\\n  }\\n]\",\"source\":\"team\"}", "ErrorMessage": "", "StatusCode": 200, "Success": true}, "TestName": "Test5_NonStreamChat_WithAPIKey"}, {"TestDescription": "非流式聊天 - Ollama模型", "Timestamp": "2025-08-04 20:44:57", "Request": {"Endpoint": "/chat", "Method": "POST", "Body": {"model_name": "qwen3:latest", "messages": [{"content": "你好世界", "type": "text", "role": "user"}], "openai_base_url": "http://localhost:11434", "model_type": "ollama", "stream": false}}, "Response": {"Content": "{\"type\":\"team_result\",\"content\":\"[\\n  {\\n    \\\"id\\\": \\\"3e7aed65-3fc0-4882-b1e9-fb55798d54a6\\\",\\n    \\\"source\\\": \\\"ollama_agent\\\",\\n    \\\"models_usage\\\": \\\"RequestUsage(prompt_tokens=39, completion_tokens=89)\\\",\\n    \\\"metadata\\\": {},\\n    \\\"created_at\\\": \\\"2025-08-04 12:44:57.165466+00:00\\\",\\n    \\\"content\\\": \\\"<think>\\\\n好的，用户打招呼说“你好世界”，我应该回应一个友好的问候。作为AI助手，我需要保持礼貌和帮助的态度。可以简单回复“你好！有什么我可以帮助你的吗？”，这样既回应了问候，又主动提供帮助，符合用户的需求。不需要使用工具，直接回答即可。确认没有其他任务需要处理，可以终止了。\\\\n</think>\\\\n\\\\n你好！有什么我可以帮助你的吗？\\\",\\n    \\\"type\\\": \\\"TextMessage\\\"\\n  }\\n]\",\"source\":\"team\"}", "ErrorMessage": "", "StatusCode": 200, "Success": true}, "TestName": "Test6_NonStreamChat_Ollama"}, {"TestDescription": "流式聊天 - 基本对话", "Timestamp": "2025-08-04 20:44:58", "Request": {"Endpoint": "/chat/stream", "Method": "POST", "Body": {"messages": [{"content": "写一首关于春天的诗", "type": "text", "role": "user"}], "model_type": "openai", "openai_base_url": "https://gnomic.nengyongai.cn/v1", "stream": true, "api_key": "sk-aOnHdMDqN5SXgxwAC50298EbCcA54345B906AfC7F5D7B384", "model_name": "gpt-3.5-turbo"}}, "Response": {"Content": "data: {\"id\": \"bbefc3bb-b38d-47d1-b925-5301078a39d1\", \"source\": \"openai_agent\", \"models_usage\": \"RequestUsage(prompt_tokens=47, completion_tokens=59)\", \"metadata\": {}, \"created_at\": \"2025-08-04 12:44:58.957363+00:00\", \"content\": \"春风拂面温柔柔，\\n花开蝶舞笑语间。\\n燕儿穿梭忙寻伴，\\n春雨滋润大地安。\\n\\nTERMINATE\", \"type\": \"TextMessage\"}\n\ndata: {\"messages\": [\"id='65b1ab33-44db-444c-9ac9-3ad6864130db' source='user' models_usage=None metadata={} created_at=datetime.datetime(2025, 8, 4, 12, 44, 57, 178431, tzinfo=datetime.timezone.utc) content='写一首关于春天的诗' type='TextMessage'\", \"id='bbefc3bb-b38d-47d1-b925-5301078a39d1' source='openai_agent' models_usage=RequestUsage(prompt_tokens=47, completion_tokens=59) metadata={} created_at=datetime.datetime(2025, 8, 4, 12, 44, 58, 957363, tzinfo=datetime.timezone.utc) content='春风拂面温柔柔，\\\\n花开蝶舞笑语间。\\\\n燕儿穿梭忙寻伴，\\\\n春雨滋润大地安。\\\\n\\\\nTERMINATE' type='TextMessage'\"], \"stop_reason\": null}\n\ndata: {\"type\": \"end\"}\n\n", "ErrorMessage": "", "StatusCode": 200, "Success": true}, "TestName": "Test7_StreamChat_Basic"}, {"TestDescription": "流式聊天 - 讲笑话", "Timestamp": "2025-08-04 20:45:00", "Request": {"Endpoint": "/chat/stream", "Method": "POST", "Body": {"messages": [{"content": "讲个笑话", "type": "text", "role": "user"}], "model_type": "openai", "openai_base_url": "https://gnomic.nengyongai.cn/v1", "stream": true, "api_key": "sk-aOnHdMDqN5SXgxwAC50298EbCcA54345B906AfC7F5D7B384", "model_name": "gpt-3.5-turbo"}}, "Response": {"Content": "data: {\"id\": \"c09fe79a-2b17-4c7c-8a49-cc5b5da68051\", \"source\": \"openai_agent\", \"models_usage\": \"RequestUsage(prompt_tokens=41, completion_tokens=30)\", \"metadata\": {}, \"created_at\": \"2025-08-04 12:45:00.588944+00:00\", \"content\": \"为什么冰箱藏不住歌手？因为会唱瓶中冷。\\n\\nTERMINATE\", \"type\": \"TextMessage\"}\n\ndata: {\"messages\": [\"id='373e8e9d-7e21-48be-a5b5-fd1687e5cef0' source='user' models_usage=None metadata={} created_at=datetime.datetime(2025, 8, 4, 12, 44, 58, 974315, tzinfo=datetime.timezone.utc) content='讲个笑话' type='TextMessage'\", \"id='c09fe79a-2b17-4c7c-8a49-cc5b5da68051' source='openai_agent' models_usage=RequestUsage(prompt_tokens=41, completion_tokens=30) metadata={} created_at=datetime.datetime(2025, 8, 4, 12, 45, 0, 588944, tzinfo=datetime.timezone.utc) content='为什么冰箱藏不住歌手？因为会唱瓶中冷。\\\\n\\\\nTERMINATE' type='TextMessage'\"], \"stop_reason\": null}\n\ndata: {\"type\": \"end\"}\n\n", "ErrorMessage": "", "StatusCode": 200, "Success": true}, "TestName": "Test8_StreamChat_Joke"}, {"TestDescription": "多轮对话测试", "Timestamp": "2025-08-04 20:45:03", "Request": {"Endpoint": "/chat", "Method": "POST", "Body": {"messages": [{"content": "我想学习编程", "type": "text", "role": "user"}, {"content": "很好！编程是一项很有用的技能。你想学习哪种编程语言呢？", "type": "text", "role": "assistant"}, {"content": "我想学Python", "type": "text", "role": "user"}], "model_type": "openai", "openai_base_url": "https://gnomic.nengyongai.cn/v1", "stream": false, "api_key": "sk-aOnHdMDqN5SXgxwAC50298EbCcA54345B906AfC7F5D7B384", "model_name": "gpt-3.5-turbo"}}, "Response": {"Content": "{\"type\":\"team_result\",\"content\":\"[\\n  {\\n    \\\"id\\\": \\\"bfa23c4a-c0a1-4dc6-83d5-601d0c480903\\\",\\n    \\\"source\\\": \\\"openai_agent\\\",\\n    \\\"models_usage\\\": \\\"RequestUsage(prompt_tokens=93, completion_tokens=134)\\\",\\n    \\\"metadata\\\": {},\\n    \\\"created_at\\\": \\\"2025-08-04 12:45:03.083434+00:00\\\",\\n    \\\"content\\\": \\\"学习Python是一个很好的选择！你可以从专门的网站、教程或书籍入门。还有许多在线课程和资源可供你学习。开始尝试编写一些简单的Python代码，逐渐积累经验吧。祝你学习顺利！如需学习资源或帮助，请随时问我。现在你可以开始通过编写代码来练习Python了。祝你学有所得！\\\\n\\\\n**TERMINATE**\\\",\\n    \\\"type\\\": \\\"TextMessage\\\"\\n  }\\n]\",\"source\":\"team\"}", "ErrorMessage": "", "StatusCode": 200, "Success": true}, "TestName": "Test9_MultiTurnChat"}, {"TestDescription": "带MCP服务器URL的请求", "Timestamp": "2025-08-04 20:45:06", "Request": {"Endpoint": "/chat", "Method": "POST", "Body": {"messages": [{"content": "测试MCP功能", "type": "text", "role": "user"}], "mcp_url_list": ["http://localhost:1111/mcp"], "model_type": "openai", "openai_base_url": "https://gnomic.nengyongai.cn/v1", "stream": false, "api_key": "sk-aOnHdMDqN5SXgxwAC50298EbCcA54345B906AfC7F5D7B384", "model_name": "gpt-3.5-turbo"}}, "Response": {"Content": "{\"type\":\"team_result\",\"content\":\"[\\n  {\\n    \\\"id\\\": \\\"980e5c8a-4ef2-4eb3-9ec8-efe4a6b720ea\\\",\\n    \\\"source\\\": \\\"openai_agent\\\",\\n    \\\"models_usage\\\": \\\"RequestUsage(prompt_tokens=242, completion_tokens=13)\\\",\\n    \\\"metadata\\\": {},\\n    \\\"created_at\\\": \\\"2025-08-04 12:45:05.384322+00:00\\\",\\n    \\\"content\\\": [\\n      \\\"FunctionCall(id='call_xEovh4FGRdC13wkoqzMn2M8P', arguments='{\\\\\\\"x\\\\\\\":100}', name='log')\\\"\\n    ],\\n    \\\"type\\\": \\\"ToolCallRequestEvent\\\"\\n  },\\n  {\\n    \\\"id\\\": \\\"9784a48a-b70e-475b-88d9-5de1997628bf\\\",\\n    \\\"source\\\": \\\"openai_agent\\\",\\n    \\\"models_usage\\\": null,\\n    \\\"metadata\\\": {},\\n    \\\"created_at\\\": \\\"2025-08-04 12:45:05.442202+00:00\\\",\\n    \\\"content\\\": [\\n      \\\"content='2.0' name='log' call_id='call_xEovh4FGRdC13wkoqzMn2M8P' is_error=False\\\"\\n    ],\\n    \\\"type\\\": \\\"ToolCallExecutionEvent\\\"\\n  },\\n  {\\n    \\\"id\\\": \\\"fdb9e9ac-fc0a-4655-afdd-f1575b97c6b0\\\",\\n    \\\"source\\\": \\\"openai_agent\\\",\\n    \\\"models_usage\\\": \\\"RequestUsage(prompt_tokens=63, completion_tokens=24)\\\",\\n    \\\"metadata\\\": {},\\n    \\\"created_at\\\": \\\"2025-08-04 12:45:06.224126+00:00\\\",\\n    \\\"content\\\": \\\"The result of testing the MCP function with an input of 100 is 2.0. \\\\n\\\\nTERMINATE.\\\",\\n    \\\"type\\\": \\\"TextMessage\\\"\\n  }\\n]\",\"source\":\"team\"}", "ErrorMessage": "", "StatusCode": 200, "Success": true}, "TestName": "Test10_MCPRequest"}, {"TestDescription": "带团队ID的请求", "Timestamp": "2025-08-04 20:45:06", "Request": {"Endpoint": "/chat", "Method": "POST", "Body": {"messages": [{"content": "中国的首都是哪里", "type": "text", "role": "user"}], "stream": false, "team_id": 6}}, "Response": {"Content": "", "ErrorMessage": "远程服务器返回错误: (422) Unprocessable Entity。", "StatusCode": 422, "Success": false}, "TestName": "Test11_TeamIDRequest"}, {"TestDescription": "错误测试 - 无效的模型类型", "Timestamp": "2025-08-04 20:45:06", "Request": {"Endpoint": "/chat", "Method": "POST", "Body": {"model_name": "gpt-3.5-turbo", "messages": [{"content": "测试错误处理", "type": "text", "role": "user"}], "openai_base_url": "https://gnomic.nengyongai.cn/v1", "model_type": "invalid_type", "stream": false}}, "Response": {"Content": "{\"type\":\"error\",\"content\":\"No response received from chat backend\",\"source\":null}", "ErrorMessage": "", "StatusCode": 200, "Success": true}, "TestName": "Test12_ErrorTest_InvalidModelType"}, {"TestDescription": "错误测试 - 缺少必需参数", "Timestamp": "2025-08-04 20:45:06", "Request": {"Endpoint": "/chat", "Method": "POST", "Body": {"messages": [{"content": "测试缺少参数", "type": "text", "role": "user"}]}}, "Response": {"Content": "", "ErrorMessage": "远程服务器返回错误: (422) Unprocessable Entity。", "StatusCode": 422, "Success": false}, "TestName": "Test13_ErrorTest_MissingParams"}, {"TestDescription": "错误测试 - 无效的JSON格式", "Timestamp": "2025-08-04 20:45:06", "Request": {"Endpoint": "/chat", "Method": "POST", "Body": "Invalid JSON"}, "Response": {"Content": "", "ErrorMessage": "远程服务器返回错误: (422) Unprocessable Entity。", "StatusCode": 422, "Success": false}, "TestName": "Test14_ErrorTest_InvalidJSON"}, {"TestDescription": "图片消息测试", "Timestamp": "2025-08-04 20:45:09", "Request": {"Endpoint": "/chat", "Method": "POST", "Body": {"messages": [{"content": "1_1752497070478.png", "type": "image", "role": "user"}, {"content": "图片中讲述了什么内容", "type": "text", "role": "user"}], "model_type": "openai", "openai_base_url": "https://gnomic.nengyongai.cn/v1", "stream": false, "api_key": "sk-aOnHdMDqN5SXgxwAC50298EbCcA54345B906AfC7F5D7B384", "model_name": "gpt-4o-mini"}}, "Response": {"Content": "{\"type\":\"team_result\",\"content\":\"[\\n  {\\n    \\\"id\\\": \\\"c17efdad-4074-42a5-a18d-5c9fe956b307\\\",\\n    \\\"source\\\": \\\"openai_agent\\\",\\n    \\\"models_usage\\\": \\\"RequestUsage(prompt_tokens=8572, completion_tokens=50)\\\",\\n    \\\"metadata\\\": {},\\n    \\\"created_at\\\": \\\"2025-08-04 12:45:09.170863+00:00\\\",\\n    \\\"content\\\": \\\"图片中显示了一段代码，可能是用于处理图像的Python函数。该函数涉及到图像的边界类型和大小调整。具体内容包括使用OpenCV库的`resize`函数来改变图像的大小。\\\",\\n    \\\"type\\\": \\\"TextMessage\\\"\\n  }\\n]\",\"source\":\"team\"}", "ErrorMessage": "", "StatusCode": 200, "Success": true}, "TestName": "Test15_ImageMessage"}]}