# PowerShell script for testing AutoGen Studio Chat API with JSON output
# Set console encoding to UTF-8
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8
# Set input/output encoding to UTF-8 for proper Chinese character handling
$OutputEncoding = [System.Text.Encoding]::UTF8
[System.Console]::InputEncoding = [System.Text.Encoding]::UTF8
[System.Console]::OutputEncoding = [System.Text.Encoding]::UTF8

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "AutoGen Studio Chat API 测试脚本 (JSON输出版本)" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# 设置全局变量
$global:API_KEY = "sk-aOnHdMDqN5SXgxwAC50298EbCcA54345B906AfC7F5D7B384"
$global:OPENAI_BASE_URL = "https://gnomic.nengyongai.cn/v1"
$BASE_URL = "http://localhost:1113"

# 初始化测试结果数组
$global:TestResults = @()

# 生成时间戳用于文件名
$timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
$outputFile = "test_results_$timestamp.json"

Write-Host "[INFO] 开始测试 AutoGen Studio Chat API..." -ForegroundColor Green
Write-Host "[INFO] 基础URL: $BASE_URL" -ForegroundColor Green
Write-Host "[INFO] API密钥: $global:API_KEY" -ForegroundColor Green
Write-Host "[INFO] OpenAI基础URL: $global:OPENAI_BASE_URL" -ForegroundColor Green
Write-Host "[INFO] 测试结果将保存到: $outputFile" -ForegroundColor Green
Write-Host ""

# 辅助函数：添加测试结果
function Add-TestResult {
    param(
        [string]$TestName,
        [string]$TestDescription,
        [string]$Method,
        [string]$Endpoint,
        [object]$RequestBody,
        [int]$StatusCode,
        [string]$ResponseContent,
        [string]$ErrorMessage = "",
        [bool]$Success
    )
    
    $testResult = @{
        TestName = $TestName
        TestDescription = $TestDescription
        Timestamp = (Get-Date -Format "yyyy-MM-dd HH:mm:ss")
        Request = @{
            Method = $Method
            Endpoint = $Endpoint
            Body = $RequestBody
        }
        Response = @{
            StatusCode = $StatusCode
            Content = $ResponseContent
            ErrorMessage = $ErrorMessage
            Success = $Success
        }
    }
    
    $global:TestResults += $testResult
    
    # 显示测试结果摘要
    if ($Success) {
        Write-Host "✓ $TestName - 成功 (状态码: $StatusCode)" -ForegroundColor Green
    } else {
        Write-Host "✗ $TestName - 失败 (状态码: $StatusCode)" -ForegroundColor Red
        if ($ErrorMessage) {
            Write-Host "  错误: $ErrorMessage" -ForegroundColor Red
        }
    }
}

# 辅助函数：执行HTTP请求并记录结果
function Invoke-TestRequest {
    param(
        [string]$TestName,
        [string]$TestDescription,
        [string]$Method,
        [string]$Endpoint,
        [object]$RequestBody = $null,
        [string]$ContentType = "application/json"
    )
    
    $fullUrl = "$BASE_URL$Endpoint"
    
    try {
        if ($Method -eq "GET") {
            $response = Invoke-WebRequest -Uri $fullUrl -Method $Method -ContentType $ContentType -UseBasicParsing
        } else {
            if ($RequestBody) {
                $bodyJson = $RequestBody | ConvertTo-Json -Depth 10
                $bodyBytes = [System.Text.Encoding]::UTF8.GetBytes($bodyJson)
                $response = Invoke-WebRequest -Uri $fullUrl -Method $Method -Body $bodyBytes -ContentType "$ContentType; charset=utf-8" -UseBasicParsing
            } else {
                $response = Invoke-WebRequest -Uri $fullUrl -Method $Method -ContentType $ContentType -UseBasicParsing
            }
        }
        
        Add-TestResult -TestName $TestName -TestDescription $TestDescription -Method $Method -Endpoint $Endpoint -RequestBody $RequestBody -StatusCode $response.StatusCode -ResponseContent $response.Content -Success $true
        
    } catch {
        $statusCode = if ($_.Exception.Response) { $_.Exception.Response.StatusCode.value__ } else { 0 }
        Add-TestResult -TestName $TestName -TestDescription $TestDescription -Method $Method -Endpoint $Endpoint -RequestBody $RequestBody -StatusCode $statusCode -ResponseContent "" -ErrorMessage $_.Exception.Message -Success $false
    }
}

# 测试1: 根端点 - 获取API基本信息
Write-Host "执行测试1: 获取API基本信息..." -ForegroundColor Yellow
Invoke-TestRequest -TestName "Test1_RootEndpoint" -TestDescription "获取API基本信息" -Method "GET" -Endpoint "/"

# 测试2: 健康检查
Write-Host "执行测试2: 健康检查..." -ForegroundColor Yellow
Invoke-TestRequest -TestName "Test2_HealthCheck" -TestDescription "健康检查" -Method "GET" -Endpoint "/health"

# 测试3: 非流式聊天 - 基本对话
Write-Host "执行测试3: 非流式聊天 - 基本对话..." -ForegroundColor Yellow
$body3 = @{
    messages = @(
        @{
            type = "text"
            content = "你好，请介绍一下你自己"
            role = "user"
        }
    )
    model_name = "gpt-3.5-turbo"
    model_type = "openai"
    api_key = $global:API_KEY
    openai_base_url = $global:OPENAI_BASE_URL
    stream = $false
}
Invoke-TestRequest -TestName "Test3_NonStreamChat_Basic" -TestDescription "非流式聊天 - 基本对话" -Method "POST" -Endpoint "/chat" -RequestBody $body3

# 测试4: 非流式聊天 - 机器学习问题
Write-Host "执行测试4: 非流式聊天 - 机器学习问题..." -ForegroundColor Yellow
$body4 = @{
    messages = @(
        @{
            type = "text"
            content = "解释一下机器学习的基本概念"
            role = "user"
        }
    )
    model_name = "gpt-3.5-turbo"
    model_type = "openai"
    api_key = $global:API_KEY
    openai_base_url = $global:OPENAI_BASE_URL
    stream = $false
}
Invoke-TestRequest -TestName "Test4_NonStreamChat_ML" -TestDescription "非流式聊天 - 机器学习问题" -Method "POST" -Endpoint "/chat" -RequestBody $body4

# 测试5: 非流式聊天 - 带API密钥
Write-Host "执行测试5: 非流式聊天 - 带API密钥..." -ForegroundColor Yellow
$body5 = @{
    messages = @(
        @{
            type = "text"
            content = "写一个Python函数来计算斐波那契数列"
            role = "user"
        }
    )
    model_name = "gpt-3.5-turbo"
    model_type = "openai"
    api_key = $global:API_KEY
    openai_base_url = $global:OPENAI_BASE_URL
    stream = $false
}
Invoke-TestRequest -TestName "Test5_NonStreamChat_WithAPIKey" -TestDescription "非流式聊天 - 带API密钥" -Method "POST" -Endpoint "/chat" -RequestBody $body5

# 测试6: 非流式聊天 - Ollama模型
Write-Host "执行测试6: 非流式聊天 - Ollama模型..." -ForegroundColor Yellow
$body6 = @{
    messages = @(
        @{
            type = "text"
            content = "你好世界"
            role = "user"
        }
    )
    model_name = "qwen3:latest"
    model_type = "ollama"
    openai_base_url = "http://localhost:11434"
    stream = $false
}
Invoke-TestRequest -TestName "Test6_NonStreamChat_Ollama" -TestDescription "非流式聊天 - Ollama模型" -Method "POST" -Endpoint "/chat" -RequestBody $body6

# 测试7: 流式聊天 - 基本对话
Write-Host "执行测试7: 流式聊天 - 基本对话..." -ForegroundColor Yellow
$body7 = @{
    messages = @(
        @{
            type = "text"
            content = "写一首关于春天的诗"
            role = "user"
        }
    )
    model_name = "gpt-3.5-turbo"
    model_type = "openai"
    api_key = $global:API_KEY
    openai_base_url = $global:OPENAI_BASE_URL
    stream = $true
}
Invoke-TestRequest -TestName "Test7_StreamChat_Basic" -TestDescription "流式聊天 - 基本对话" -Method "POST" -Endpoint "/chat/stream" -RequestBody $body7

# 测试8: 流式聊天 - 讲笑话
Write-Host "执行测试8: 流式聊天 - 讲笑话..." -ForegroundColor Yellow
$body8 = @{
    messages = @(
        @{
            type = "text"
            content = "讲个笑话"
            role = "user"
        }
    )
    model_name = "gpt-3.5-turbo"
    model_type = "openai"
    api_key = $global:API_KEY
    openai_base_url = $global:OPENAI_BASE_URL
    stream = $true
}
Invoke-TestRequest -TestName "Test8_StreamChat_Joke" -TestDescription "流式聊天 - 讲笑话" -Method "POST" -Endpoint "/chat/stream" -RequestBody $body8

# 测试9: 多轮对话测试
Write-Host "执行测试9: 多轮对话测试..." -ForegroundColor Yellow
$body9 = @{
    messages = @(
        @{
            type = "text"
            content = "我想学习编程"
            role = "user"
        },
        @{
            type = "text"
            content = "很好！编程是一项很有用的技能。你想学习哪种编程语言呢？"
            role = "assistant"
        },
        @{
            type = "text"
            content = "我想学Python"
            role = "user"
        }
    )
    model_name = "gpt-3.5-turbo"
    model_type = "openai"
    api_key = $global:API_KEY
    openai_base_url = $global:OPENAI_BASE_URL
    stream = $false
}
Invoke-TestRequest -TestName "Test9_MultiTurnChat" -TestDescription "多轮对话测试" -Method "POST" -Endpoint "/chat" -RequestBody $body9

# 测试10: 带MCP服务器URL的请求
Write-Host "执行测试10: 带MCP服务器URL的请求..." -ForegroundColor Yellow
$body10 = @{
    messages = @(
        @{
            type = "text"
            content = "测试MCP功能"
            role = "user"
        }
    )
    model_name = "gpt-3.5-turbo"
    model_type = "openai"
    api_key = $global:API_KEY
    openai_base_url = $global:OPENAI_BASE_URL
    mcp_url_list = @("http://localhost:1111/mcp")
    stream = $false
}
Invoke-TestRequest -TestName "Test10_MCPRequest" -TestDescription "带MCP服务器URL的请求" -Method "POST" -Endpoint "/chat" -RequestBody $body10

# 测试11: 带团队ID的请求
Write-Host "执行测试11: 带团队ID的请求..." -ForegroundColor Yellow
$body11 = @{
    messages = @(
        @{
            type = "text"
            content = "中国的首都是哪里"
            role = "user"
        }
    )
    team_id = 6
    stream = $false
}
Invoke-TestRequest -TestName "Test11_TeamIDRequest" -TestDescription "带团队ID的请求" -Method "POST" -Endpoint "/chat" -RequestBody $body11

# 测试12: 错误测试 - 无效的模型类型
Write-Host "执行测试12: 错误测试 - 无效的模型类型..." -ForegroundColor Yellow
$body12 = @{
    messages = @(
        @{
            type = "text"
            content = "测试错误处理"
            role = "user"
        }
    )
    model_name = "gpt-3.5-turbo"
    model_type = "invalid_type"
    openai_base_url = $global:OPENAI_BASE_URL
    stream = $false
}
Invoke-TestRequest -TestName "Test12_ErrorTest_InvalidModelType" -TestDescription "错误测试 - 无效的模型类型" -Method "POST" -Endpoint "/chat" -RequestBody $body12

# 测试13: 错误测试 - 缺少必需参数
Write-Host "执行测试13: 错误测试 - 缺少必需参数..." -ForegroundColor Yellow
$body13 = @{
    messages = @(
        @{
            type = "text"
            content = "测试缺少参数"
            role = "user"
        }
    )
}
Invoke-TestRequest -TestName "Test13_ErrorTest_MissingParams" -TestDescription "错误测试 - 缺少必需参数" -Method "POST" -Endpoint "/chat" -RequestBody $body13

# 测试14: 错误测试 - 无效的JSON格式
Write-Host "执行测试14: 错误测试 - 无效的JSON格式..." -ForegroundColor Yellow
try {
    $invalidJson = "{invalid json}"
    $response = Invoke-WebRequest -Uri "$BASE_URL/chat" -Method POST -Body $invalidJson -ContentType "application/json" -UseBasicParsing
    Add-TestResult -TestName "Test14_ErrorTest_InvalidJSON" -TestDescription "错误测试 - 无效的JSON格式" -Method "POST" -Endpoint "/chat" -RequestBody "Invalid JSON" -StatusCode $response.StatusCode -ResponseContent $response.Content -Success $true
} catch {
    $statusCode = if ($_.Exception.Response) { $_.Exception.Response.StatusCode.value__ } else { 0 }
    Add-TestResult -TestName "Test14_ErrorTest_InvalidJSON" -TestDescription "错误测试 - 无效的JSON格式" -Method "POST" -Endpoint "/chat" -RequestBody "Invalid JSON" -StatusCode $statusCode -ResponseContent "" -ErrorMessage $_.Exception.Message -Success $false
}

# 测试15: 图片消息测试
Write-Host "执行测试15: 图片消息测试..." -ForegroundColor Yellow
$body15 = @{
    messages = @(
        @{
            type = "image"
            content = "1_1752497070478.png"
            role = "user"
        },
        @{
            type = "text"
            content = "图片中讲述了什么内容"
            role = "user"
        }
    )
    model_name = "gpt-4o-mini"
    model_type = "openai"
    api_key = $global:API_KEY
    openai_base_url = $global:OPENAI_BASE_URL
    stream = $false
}
Invoke-TestRequest -TestName "Test15_ImageMessage" -TestDescription "图片消息测试" -Method "POST" -Endpoint "/chat" -RequestBody $body15

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "测试完成！正在生成JSON报告..." -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan

# 创建完整的测试报告
$testReport = @{
    TestSuite = "AutoGen Studio Chat API Tests"
    ExecutionTime = (Get-Date -Format "yyyy-MM-dd HH:mm:ss")
    Configuration = @{
        BaseURL = $BASE_URL
        APIKey = $global:API_KEY
        OpenAIBaseURL = $global:OPENAI_BASE_URL
    }
    Summary = @{
        TotalTests = $global:TestResults.Count
        PassedTests = ($global:TestResults | Where-Object { $_.Response.Success -eq $true }).Count
        FailedTests = ($global:TestResults | Where-Object { $_.Response.Success -eq $false }).Count
    }
    TestResults = $global:TestResults
}

# 计算成功率
$successRate = if ($testReport.Summary.TotalTests -gt 0) {
    [math]::Round(($testReport.Summary.PassedTests / $testReport.Summary.TotalTests) * 100, 2)
} else {
    0
}
$testReport.Summary.SuccessRate = "$successRate%"

# 将结果转换为JSON并保存到文件
try {
    $jsonOutput = $testReport | ConvertTo-Json -Depth 20
    # 确保使用UTF-8编码保存文件
    [System.IO.File]::WriteAllText($outputFile, $jsonOutput, [System.Text.Encoding]::UTF8)

    Write-Host "[SUCCESS] 测试结果已保存到: $outputFile" -ForegroundColor Green
    Write-Host ""

    # 显示测试摘要
    Write-Host "测试摘要:" -ForegroundColor White
    Write-Host "========================================" -ForegroundColor White
    Write-Host "总测试数: $($testReport.Summary.TotalTests)" -ForegroundColor White
    Write-Host "成功测试: $($testReport.Summary.PassedTests)" -ForegroundColor Green
    Write-Host "失败测试: $($testReport.Summary.FailedTests)" -ForegroundColor Red
    Write-Host "成功率: $($testReport.Summary.SuccessRate)" -ForegroundColor $(if ($testReport.Summary.PassedTests -eq $testReport.Summary.TotalTests) { "Green" } else { "Yellow" })
    Write-Host ""

    # 显示失败的测试
    $failedTests = $global:TestResults | Where-Object { $_.Response.Success -eq $false }
    if ($failedTests.Count -gt 0) {
        Write-Host "失败的测试:" -ForegroundColor Red
        Write-Host "========================================" -ForegroundColor Red
        foreach ($test in $failedTests) {
            Write-Host "- $($test.TestName): $($test.Response.ErrorMessage)" -ForegroundColor Red
        }
        Write-Host ""
    }

    Write-Host "详细测试结果已保存到JSON文件中，可以使用以下命令查看:" -ForegroundColor Cyan
    Write-Host "Get-Content '$outputFile' | ConvertFrom-Json | ConvertTo-Json -Depth 20" -ForegroundColor Cyan
    Write-Host ""

} catch {
    Write-Host "[ERROR] 保存测试结果时出错: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "测试用例说明:" -ForegroundColor White
Write-Host "1-2:   基础功能测试 (根端点、健康检查)" -ForegroundColor White
Write-Host "3-6:   非流式聊天测试 (不同模型和参数)" -ForegroundColor White
Write-Host "7-8:   流式聊天测试" -ForegroundColor White
Write-Host "9:     多轮对话测试" -ForegroundColor White
Write-Host "10-11: 高级功能测试 (MCP、团队ID)" -ForegroundColor White
Write-Host "12-14: 错误处理测试" -ForegroundColor White
Write-Host "15:    图片消息测试" -ForegroundColor White
Write-Host ""

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "AutoGen Studio Chat API 测试完成！" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
